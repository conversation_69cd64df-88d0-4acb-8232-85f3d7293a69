<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上海市设施农业装备行业协会</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
    .full-width-section {
        background-color: #f9f9f9;
        padding: 3rem 0;
        margin: 2rem 0;
    }

    .full-width-section .container {
        margin: 0 auto;
    }

    /* 菜单状态指示器样式 */
    .menu-status {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background-color: rgba(0,0,0,0.7);
        color: white;
        padding: 10px;
        border-radius: 5px;
        z-index: 1000;
    }
    .menu-status.open {
        background-color: rgba(76, 175, 80, 0.7);
    }
    .menu-status.closed {
        background-color: rgba(244, 67, 54, 0.7);
    }
    </style>
</head>
<body>
    <header>
        <h1>上海市设施农业装备行业协会会</h1>
    </header>
    <nav>
        <div class="menu-toggle">
            <span></span>
            <span></span>
            <span></span>
        </div>
        <ul class="nav-menu">
            <li><a href="index.html">首页</a></li>
            <li class="dropdown">
                <a href="index.html">协会简介</a>
                <ul class="dropdown-menu">
                    <li><a href="#">了解协会</a></li>
                    <li><a href="#">组织结构</a></li>
                    <li><a href="#">协会负责人</a></li>
                    <li><a href="#">入会流程</a></li>
                </ul>
            </li> 
            <li class="dropdown">
                <a href="#">行业动态</a>
                <ul class="dropdown-menu">
                    <li><a href="#">协会章程</a></li>
                    <li><a href="#">理事会成员</a></li>
                    <li><a href="#">协会荣誉</a></li>
                    <li><a href="#">发展历程</a></li>
                </ul>
            </li>
            <li class="dropdown">
                <a href="#">协会动态</a>
                <ul class="dropdown-menu">
                    <li><a href="#">协会风采</a></li>
                    <li><a href="#">协会活动</a></li>
                </ul>
            </li>
            <li><a href="#">会员单位</a></li>
            <li><a href="#">公告</a></li>
        </ul>
    </nav>
    <div class="carousel">
        <div class="carousel-slide active">
            <img crossorigin="anonymous" src="images/index-1.jpg" alt="协会活动图片1">
            <div class="carousel-caption">
                <h3>2025年蔬菜产业大会</h3>
                <p>第十届知名蔬菜销售商走进宁夏活动成功举办</p>
            </div>
        </div>
        <div class="carousel-slide">
            <img crossorigin="anonymous" src="images/index-2.jpg" alt="协会活动图片2">
            <div class="carousel-caption">
                <h3>奉贤南瓜丰收季</h3>
                <p>上海曹野农业发展有限公司种植基地喜获丰收</p>
            </div>
        </div>
        <div class="carousel-slide">
            <img crossorigin="anonymous" src="images/index-3.jpg" alt="协会活动图片3">
            <div class="carousel-caption">
                <h3>会员企业交流座谈会</h3>
                <p>探讨行业发展趋势及合作机遇</p>
            </div>
        </div>
        <button class="carousel-prev">❮</button>
        <button class="carousel-next">❯</button>
    </div>
    <div class="container">
        <div class="main-content">
              <div class="container">
            <div class="content-columns">
                <section class="news column">
                    <h2>行业资讯</h2>
                    <div class="news-list">
                        <div class="news-item">
                            <div class="news-image"><img src="images/blog-1.jpg" alt="行业资讯图片" style="width:100%;height:100%;object-fit:cover;border-radius:4px;"></div>
                            <div class="news-content">
                                <h3><a href="news-detail.html">“竹节草”或在奉贤到浦东新区南部一带二次登陆！今夜明天风雨最明显→</a></h3>
                                <span class="date">沪蔬菇 - 2025年7月15日</span>
                                <p>气象部门发布紧急预警，台风“竹节草”可能于今夜在奉贤至浦东新区南部一带二次登陆，将带来明显风雨影响。</p>
                            </div>
                        </div>
                        <div class="news-item">
                            <div class="news-image"><img src="images/blog-2.jpg" alt="协会简报图片" style="width:100%;height:100%;object-fit:cover;border-radius:4px;"></div>
                            <div class="news-content">
                                <h3><a href="news-detail.html">协会简报2025年7期</a></h3>
                                <span class="date">沪蔬菇 - 2025年7月10日</span>
                                <p>本期简报聚焦行业政策解读、会员动态及 upcoming 培训活动预告，为会员单位提供最新行业资讯。</p>
                            </div>
                        </div>
                        <div class="news-item">
                            <div class="news-image"><img src="images/blog-3.jpg" alt="南瓜丰收图片" style="width:100%;height:100%;object-fit:cover;border-radius:4px;"></div>
                            <div class="news-content">
                                <h3><a href="news-detail.html">奉贤南瓜迎来今年首季丰收</a></h3>
                                <span class="date">沪蔬菇 - 2025年7月5日</span>
                                <p>奉贤区南瓜种植基地喜获丰收，今年产量预计同比增长15%，优质南瓜将陆续供应市场。</p>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="news column">
                    <h2>协会公告</h2>
                    <div class="news-list">
                        <div class="news-item">
                            <div class="news-content no-image">
                                <h3><a href="news-detail.html">2025年蔬菜产业大会暨第十届知名蔬菜销售商走进宁夏活动成功举办</a></h3>
                            </div>
                        </div>
                        <div class="news-item">
                            <div class="news-content no-image">
                                <h3><a href="news-detail.html">关于AGR2025环球农产品地理标志及区域品牌展的参展通知</a></h3>
                            </div>
                        </div>
                        <div class="news-item">
                            <div class="news-content no-image">
                                <h3><a href="news-detail.html">我协会积极参加“千行百业践初心 凝新聚力惠民生”2025年新兴领域党员志愿服务活动</a></h3>
                            </div>
                        </div>
                        <div class="news-item">
                            <div class="news-content no-image">
                                <h3><a href="news-detail.html">吴梦秋会长携劳模精神进校园-协会助力培育新时代高素质“农人”</a></h3>
                            </div>
                        </div>
                        <div class="news-item">
                            <div class="news-content no-image">
                                <h3><a href="news-detail.html">协会党支部召开深入贯彻《中央八项规定》精神学习教育主题学习会</a></h3>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
            <div class="content-columns">
                <section class="news column">
                    <h2>协会风采</h2>
                    <div class="news-list">
                        <div class="news-item">
                            <div class="news-image"><img src="images/blog-3.jpg" alt="协会风采图片" style="width:100%;height:100%;object-fit:cover;border-radius:4px;"></div>
                            <div class="news-content">
                                <h3><a href="news-detail.html">协会荣获2024年度优秀行业组织称号</a></h3>
                                <span class="date">沪蔬菇 - 2024年12月20日</span>
                                <p>在上海市行业协会评选中，我会凭借在蔬菜食用菌产业的突出贡献，荣获优秀行业组织称号。</p>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="news column">
                    <h2>协会活动</h2>
                    <div class="news-list">
                        <div class="news-item">
                            <div class="news-image"><img src="images/blog-1.jpg" alt="协会活动图片" style="width:100%;height:100%;object-fit:cover;border-radius:4px;"></div>
                            <div class="news-content">
                                <h3><a href="news-detail.html">2025年会员企业交流座谈会顺利召开</a></h3>
                                <span class="date">沪蔬菇 - 2025年5月10日</span>
                                <p>协会组织会员企业开展交流座谈会，探讨行业发展趋势及合作机遇，促进企业间资源共享。</p>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>
  </div>
    <div class="full-width-section">
        <div class="container">
            <div class="section-header">
                <h2>最新资讯与文章</h2>
                <p>本栏目汇集行业最新动态与深度分析，为会员单位提供及时、准确的资讯服务。</p>
            </div>
            <div class="content-columns">
                <section class="news column">
                    <div class="date-badge">08 Jun 24</div>
                    <div class="news-image"><img src="images/blog-2.jpg" alt="奶牛饲养图片" style="width:100%;height:100%;object-fit:cover;border-radius:4px;"></div>
                    <div class="news-content">
                        <h3><a href="news-detail.html">如何照顾奶牛以获得最佳品质的肉</a></h3>
                        <p>详细介绍奶牛饲养的关键技术和管理方法，确保产出高品质的肉类产品。</p>
                    </div>
                </section>
                <section class="news column">
                    <div class="date-badge">08 Jun 24</div>
                    <div class="news-image"><img src="images/blog-1.jpg" alt="玉米收获图片" style="width:100%;height:100%;object-fit:cover;border-radius:4px;"></div>
                    <div class="news-content">
                        <h3><a href="news-detail.html">收获玉米的最佳时间</a></h3>
                        <p>探讨玉米收获的最佳时机和技术，确保玉米品质和产量最大化。</p>
                    </div>
                </section>
                <section class="news column">
                    <div class="date-badge">08 Jul 24</div>
                    <div class="news-image"><img src="images/blog-3.jpg" alt="农场工作图片" style="width:100%;height:100%;object-fit:cover;border-radius:4px;"></div>
                    <div class="news-content">
                        <h3><a href="news-detail.html">在羊场工作的乐趣</a></h3>
                        <p>分享农场日常工作的经验和乐趣，展现农业工作的魅力。</p>
                    </div>
                </section>
            </div>
        </div>
      </div>
    <div class="bottom-nav">
        <div class="container">
            <div class="nav-column">
                <h4>协会概况</h4>
                <ul>
                    <li><a href="#">协会简介</a></li>
                    <li><a href="#">组织机构</a></li>
                    <li><a href="#">协会领导</a></li>
                    <li><a href="#">联系方式</a></li>
                </ul>
            </div>
            <div class="nav-column">
                <h4>行业资讯</h4>
                <ul>
                    <li><a href="#">行业动态</a></li>
                    <li><a href="#">政策法规</a></li>
                    <li><a href="#">市场行情</a></li>
                    <li><a href="#">技术交流</a></li>
                </ul>
            </div>
            <div class="nav-column">
                <h4>会员服务</h4>
                <ul>
                    <li><a href="#">会员介绍</a></li>
                    <li><a href="#">入会指南</a></li>
                    <li><a href="#">会员活动</a></li>
                </ul>
            </div>
            <div class="nav-column">
                <h4>下载中心</h4>
                <ul>
                    <li><a href="#">表格下载</a></li>
                    <li><a href="#">资料下载</a></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="friend-links">
        <div class="container">
            <h3>友情链接</h3>
            <div class="links-container">
                <a href="#" class="link-card">中国蔬菜协会</a>
                <a href="#" class="link-card">上海市农业农村委员会</a>
                <a href="#" class="link-card">宁夏蔬菜产业协会</a>
                <a href="#" class="link-card">全国农产品流通协会</a>
                <a href="#" class="link-card">上海农业科学院</a>
                <a href="#" class="link-card">中国食用菌协会</a>
            </div>
        </div>
    </div>
    <div class="contact-info-bottom">
        <div class="container">
            <div class="contact-logo">
                <img src="images/logo.jpg" alt="协会LOGO">
            </div>
            <div class="contact-details">
                <p>地址：上海市奉贤区金齐路1000号</p>
                <p>邮箱：<EMAIL></p>
            </div>
        </div>
    </div>
    <footer>
        <p>上海市设施农业装备行业协会 @2025 版权所有  沪ICP备000000000</p>
    </footer>

    <script>
        // 菜单切换函数
        function toggleMenu() {
            const navMenu = document.querySelector('.nav-menu');
            const menuToggle = document.querySelector('.menu-toggle');
            navMenu.classList.toggle('active');
            menuToggle.classList.toggle('active');
        }

        // 二级菜单切换函数
        function setupDropdownMenus() {
            const dropdowns = document.querySelectorAll('.dropdown');
            console.log(`Found ${dropdowns.length} dropdown menus`);

            dropdowns.forEach(dropdown => {
                const link = dropdown.querySelector('a:first-child');
                if (link) {
                    // 点击事件处理
                    link.addEventListener('click', function(e) {
                        // 阻止默认行为
                        e.preventDefault();
                        e.stopPropagation();

                        // 切换当前下拉菜单的active状态
                        dropdown.classList.toggle('active');
                        console.log(`Dropdown ${dropdown.querySelector('a').textContent} active state toggled`);

                        // 关闭其他下拉菜单
                        dropdowns.forEach(otherDropdown => {
                            if (otherDropdown !== dropdown) {
                                otherDropdown.classList.remove('active');
                            }
                        });
                    });

                    // 触摸事件处理
                    link.addEventListener('touchstart', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        dropdown.classList.toggle('active');
                        console.log(`Dropdown ${dropdown.querySelector('a').textContent} active state toggled via touch`);

                        dropdowns.forEach(otherDropdown => {
                            if (otherDropdown !== dropdown) {
                                otherDropdown.classList.remove('active');
                            }
                        });
                    }, { passive: false });
                }
            });

            // 点击页面其他地方关闭下拉菜单
            document.addEventListener('click', function() {
                dropdowns.forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            });
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 菜单状态指示器已移除

            // 菜单切换功能
            const menuToggle = document.querySelector('.menu-toggle');
            if (menuToggle) {
                menuToggle.addEventListener('click', toggleMenu);
                console.log('Menu toggle functionality has been enabled');
            }

            // 轮播图功能实现
            const slides = document.querySelectorAll('.carousel-slide');
            const prevBtn = document.querySelector('.carousel-prev');
            const nextBtn = document.querySelector('.carousel-next');
            let currentIndex = 0;
            let slideInterval;

            // 显示当前幻灯片
            function showSlide(index) {
                slides.forEach((slide, i) => {
                    slide.classList.toggle('active', i === index);
                });
            }

            // 下一张幻灯片
            function nextSlide() {
                currentIndex = (currentIndex + 1) % slides.length;
                showSlide(currentIndex);
            }

            // 上一张幻灯片
            function prevSlide() {
                currentIndex = (currentIndex - 1 + slides.length) % slides.length;
                showSlide(currentIndex);
            }

            // 自动轮播
            function startSlideInterval() {
                slideInterval = setInterval(nextSlide, 5000);
            }

            // 事件监听
            prevBtn.addEventListener('click', () => {
                prevSlide();
                clearInterval(slideInterval);
                startSlideInterval();
            });

            nextBtn.addEventListener('click', () => {
                nextSlide();
                clearInterval(slideInterval);
                startSlideInterval();
            });

            // 初始化二级菜单
            setupDropdownMenus();

            // 初始化轮播图
            showSlide(currentIndex);
            startSlideInterval();
        });
    </script>
</body>
</html>