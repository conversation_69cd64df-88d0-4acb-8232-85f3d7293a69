:root {
    --primary-color: #2E7D32; /* 主色调：深绿色 */
    --primary-light: #4CAF50; /* 浅绿色 */
    --secondary-color: #F5F5F5; /* 背景色：浅灰色 */
    --accent-color: #FFC107; /* 强调色：黄色 */
    --text-color: #333333; /* 文本颜色：深灰色 */
    --light-green: #E8F5E9; /* 浅绿色背景 */
    --border-color: #e0e0e0; /* 边框颜色 */
    --nav-height: 60px; /* 导航栏高度 */
}

.cookies-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cookies-header h3 {
    margin: 0;
    color: var(--primary-color);
}

.cookies-close {
    font-size: 28px;
    cursor: pointer;
    color: #999;
}

.cookies-close:hover {
    color: #333;
}

.cookies-content {
    padding: 20px;
}

.cookies-content p {
    margin: 0 0 20px 0;
    line-height: 1.6;
}

.cookies-option {
    margin-bottom: 15px;
}

.cookies-option label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.cookies-option input[type="checkbox"] {
    margin-right: 10px;
    width: 18px;
    height: 18px;
}

.cookies-option span {
    font-size: 14px;
}

.cookies-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    text-align: right;
}

.save-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 25px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s;
}

.save-btn:hover {
    background: #1B5E20;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    margin: 0;
    padding: 0;
    line-height: 1.4;
    color: var(--text-color);
    background-color: var(--secondary-color);
    position: relative;
}

/* 添加蒙层效果 */
/* 蒙层元素样式 */
#overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(46, 125, 50, 0.15); /* 更明显的半透明绿色背景 */
    z-index: 9999; /* 确保蒙层覆盖所有页面元素 */
    display: none; /* 默认隐藏 */
}

#overlay.overlay {
    display: block; /* 显示蒙层 */
}

header {
    background-color: var(--primary-color);
    color: white;
    padding: 1rem 0;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

nav {
    background-color: #2C3E50;
    height: var(--nav-height);
    padding: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: relative;
    z-index: 100;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    max-width: 100%;
    margin: 0;
    border-radius: 0;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    text-align: center;
}
.menu-toggle {
    display: none;
    cursor: pointer;
    position: absolute;
    right: 1.8rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 100;
}
.menu-toggle span {
    display: block;
    width: 25px;
    height: 3px;
    background-color: white;
    margin: 5px 0;
    border-radius: 3px;
}

nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}
.nav-menu {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    padding: 0;
    list-style: none;
    flex-wrap: nowrap;
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
}
@media (max-width: 768px) {
    .menu-toggle {
        display: block;
        z-index: 100;
        -webkit-tap-highlight-color: transparent; /* 移除触摸时的高亮效果 */
    }
    .nav-menu {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: #2C3E50; /* 更改为深蓝色，与导航栏颜色一致 */
        padding: 1rem;
        box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        z-index: 99;
        -webkit-transform: translateZ(0); /* 启用硬件加速 */
        transform: translateZ(0);
    }
    .nav-menu.active {
        display: flex;
    }
    /* 移动设备上二级菜单样式 */
    nav .dropdown-menu {
        position: static;
        margin-left: 1rem;
        border-left: 2px solid rgba(255,255,255,0.2);
        box-shadow: none;
        background-color: rgba(0,0,0,0.1);
    }
    /* 菜单按钮激活状态 */
    .menu-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    .menu-toggle.active span:nth-child(2) {
        opacity: 0;
    }
    .menu-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -7px);
    }
    .nav-menu li {
        margin: 0;
        width: 100%;
    }
    
    .nav-menu li a {
        display: block;
        padding: 1rem;
        color: white;
        text-decoration: none;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }
    
    .nav-menu li a:hover {
        background-color: rgba(255,255,255,0.1);
    }
    
    .nav-menu .has-submenu .submenu {
        position: static;
        display: none;
        width: 100%;
        background-color: rgba(0,0,0,0.1);
        box-shadow: none;
        border-left: 2px solid rgba(255,255,255,0.2);
        margin-left: 1rem;
    }
    
    .nav-menu .has-submenu:hover .submenu {
        display: block;
    }
}
nav .dropdown {
    position: relative;
}
nav .dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: rgba(44, 62, 80, 0.95);
    min-width: 160px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
    padding: 0.5rem 0;
    z-index: 100;
    border-radius: 0 0 4px 4px;
}



/* 确保触摸设备上的二级菜单也能正常显示 */
nav .dropdown.hover .dropdown-menu, nav .dropdown:hover .dropdown-menu,
nav .dropdown.active .dropdown-menu {
    display: block;
}
nav .dropdown:hover .dropdown-menu {
    display: block;
}
nav .dropdown-menu li {
    display: block;
}
nav .dropdown-menu a {
    display: block;
    padding: 0.5rem 1rem;
    color: white;
    text-decoration: none;
}
nav .dropdown-menu a:hover {
    background-color: #3d5a80;
    color: white;
nav .dropdown-menu a.disabled {
    color: #888;
    cursor: not-allowed;
}

/* 隐私政策弹窗样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.4);
}

.modal-content {
    background-color: #fff;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 600px;
    border-radius: 5px;
    position: relative;
}

.modal-content h2 {
    color: var(--primary-color);
    margin-top: 0;
}

.modal-content h3 {
    color: var(--primary-light);
    margin-top: 1.5em;
    margin-bottom: 0.5em;
}

.modal-content ul {
    padding-left: 20px;
}

.modal-content li {
    margin-bottom: 0.5em;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    position: absolute;
    right: 15px;
    top: 10px;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
}

#accept-privacy {
    background-color: var(--primary-color);
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    margin-top: 20px;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

#accept-privacy:hover {
    background-color: var(--primary-light);
}

/* 隐私政策横条中的COOKIES设置按钮 */
#cookies-settings {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 16px; /* 调整按钮大小 */
    margin: 0 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    white-space: nowrap;
}

#cookies-settings:hover {
    background-color: #43A047;
}   text-decoration: none;
}

/* 隐私政策横条样式 */
.privacy-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: rgba(232, 245, 233, 0.9);
    color: #333;
    padding: 15px 0;
    border-top: 1px solid #4CAF50;
    border-bottom: 1px solid #4CAF50;
    display: none;
    margin: 0;
    z-index: 1000;
}

.privacy-bar.visible {
    display: block;
}

.privacy-bar-content {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.privacy-text {
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
    flex: 1;
    min-width: 300px;
}

.privacy-link {
    color: #2E7D32;
    text-decoration: underline;
    margin-left: 5px;
}

.privacy-link:hover {
    color: #1B5E20;
}

.privacy-accept-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 16px;
    margin: 0 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    white-space: nowrap;
}

.privacy-accept-btn:hover {
    background-color: #43A047;
}

.privacy-close {
    color: #333;
    font-size: 24px;
    cursor: pointer;
    padding: 0 10px;
}

.privacy-close:hover {
    color: #2E7D32;
}

/* 贯穿式区域样式 */
.full-width-section {
    width: 100%;
    max-width: 100%;
    padding: 2rem 0;
    margin: 1rem 0;
    box-sizing: border-box;
    background-color: white;
}

/* 轮播图区域样式 */
.carousel {
    position: relative;
    width: 100%;
    height: 400px; /* 设置固定高度 */
    overflow: hidden;
    margin-top: 0;
}

.carousel-slide {
    display: none;
    width: 100%;
}

.carousel-slide.active {
    display: block;
}

.carousel img {
    width: 100%;
    max-width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: cover;
}

.carousel-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 1rem;
    text-align: center;
}

.carousel-caption h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
}

.carousel-caption h3 .highlight {
    color: var(--accent-color);
    font-weight: bold;
    margin-left: 0.5rem;
}

.carousel-caption p {
    margin: 0;
    font-size: 1rem;
}

.carousel-prev, .carousel-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0,0,0,0.5);
    color: white;
    border: none;
    padding: 0.8rem 1.2rem;
    cursor: pointer;
    font-size: 1.5rem;
}

.carousel-prev {
    left: 0;
}

.carousel-next {
    right: 0;
}

/* 最新资讯与文章区域布局 */
.full-width-section .content-columns {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    justify-content: center;
    margin-top: 1.5rem;
}

.full-width-section .news.column {
    flex: 1;
    min-width: 300px;
    max-width: 380px;
    display: flex;
    flex-direction: column;
    background-color: #f9f9f9;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.full-width-section .news-image {
    height: 200px;
    overflow: hidden;
}

.full-width-section .news-content {
    padding: 1.2rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.full-width-section .date-badge {
    background-color: var(--primary-color);
    color: white;
    display: inline-block;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    margin: 1rem 0 0.5rem 1rem;
    width: fit-content;
}

.full-width-section h3 {
    color: var(--text-color);
    margin: 0.5rem 0 1rem 0;
    font-size: 1.2rem;
}

.full-width-section .section-header p {
    color: #666;
    margin-top: 0.25rem;
    margin-bottom: 0;
    line-height: 1.4;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .full-width-section .content-columns {
        flex-direction: column;
        align-items: center;
    }
    
    .full-width-section .news.column {
        max-width: 100%;
        width: 100%;
    }
}

/* 底部导航区域样式 */
.bottom-nav {
    background-color: #2C3E50;
    color: white;
    padding: 1.5rem 0;
    margin-top: 1rem;
}

.bottom-nav .container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin: 0 auto;
    max-width: 1200px;
}

/* 添加底部导航的响应式布局 */
@media (max-width: 992px) {
    .bottom-nav .container {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 576px) {
    .bottom-nav .container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    .bottom-nav .nav-column {
        min-width: 0 !important;
        flex: 1 1 100%;
    }
}

.nav-column {
    flex: 1;
    min-width: 200px;
}

.nav-column h4 {
    color: white;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.nav-column ul {
    list-style: none;
    padding: 0;
}

.nav-column li {
    margin-bottom: 0.5rem;
}

.nav-column a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.nav-column a:hover {
    color: white;
}

/* 友情链接区域优化 */
.friend-links {
    background-color: white;
    padding: 1.5rem 0 2rem 0;
    margin-top: 0;
    margin-bottom: 0;
}

.friend-links h3 {
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 1.2rem;
    font-size: 1.4rem;
}

.links-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.8rem;
    padding: 0.5rem 0;
}

.link-card {
    background-color: var(--light-green);
    color: var(--text-color);
    padding: 0.6rem 1.2rem;
    border-radius: 20px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

/* 地址和邮箱区域优化 */
.contact-info-bottom {
    background-color: #f5f5f5;
    padding: 2rem 0;
}

.contact-info-bottom .container {
    display: flex;
    align-items: center;
    gap: 3rem;
    padding: 1rem 0;
}

.contact-logo img {
    max-height: 90px;
    margin-right: 1rem;
}

.contact-details p {
    margin: 0.3rem 0;
    line-height: 1.6;
    font-size: 1.1rem;
    color: var(--text-color);
}

/* 分类页面样式 */
.category-breadcrumb {
    margin: 1rem 0;
    padding: 0.5rem 0;
    color: #666;
    font-size: 0.9rem;
    border-bottom: 1px solid var(--border-color);
}

.category-breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.category-breadcrumb a:hover {
    text-decoration: underline;
}

.category-title {
    color: var(--primary-color);
    font-size: 1.8rem;
    margin: 1rem 0;
    line-height: 1.3;
}

.category-description {
    color: #666;
    margin-bottom: 2rem;
    padding: 0.8rem;
    background-color: var(--light-green);
    border-left: 4px solid var(--primary-color);
    border-radius: 0 4px 4px 0;
}

.content-columns .column.full-width {
    width: 100%;
    max-width: 100%;
}

/* 文章详情页样式 */
.post-detail {
    background-color: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 2rem;
}

.post-header {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.post-title {
    color: var(--primary-color);
    font-size: 1.8rem;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.post-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    color: #666;
    font-size: 0.9rem;
}

.post-content {
    line-height: 1.8;
    color: var(--text-color);
    font-size: 1.05rem;
}

.post-content p {
    margin-bottom: 1.5rem;
}

.post-content h2, .post-content h3 {
    color: var(--primary-color);
    margin: 1.5rem 0 1rem 0;
}

.post-content img {
    max-width: 100%;
    height: auto;
    margin: 1.5rem 0;
    border-radius: 4px;
}

/* 文章内容区域美化 */
.article-content {
    line-height: 1.8;
    color: var(--text-color);
    font-size: 1.05rem;
    padding: 1.5rem 0;
}

.article-content p {
    margin-bottom: 1.5rem;
    text-align: justify;
}

.article-content h2, .article-content h3 {
    color: var(--primary-color);
    margin: 1.5rem 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.article-content img {
    max-width: 100%;
    height: auto;
    margin: 1.5rem auto;
    border-radius: 8px;
    display: block;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* 底部版权区域紧凑化 */
footer {
    background-color: var(--primary-color);
    color: white;
    text-align: center;
    padding: 0.2rem 0;
    margin-top: 0;
}

footer .container p {
    margin: 0.3rem 0;
    font-size: 0.9rem;
}



/* 协会公告标题样式 - 只影响协会公告栏目 */
.news.tag-section .news-content.no-thumbnail h3 {
    font-size: 1rem; /* 字体大小保持不变 */
    margin-top: 0.2rem; /* 再次减小上边距 */
    margin-bottom: 0.2rem; /* 再次减小下边距 */
}

/* 协会公告新闻项间距调整 - 只影响协会公告栏目 */
.news.tag-section .news-item:has(.news-content.no-thumbnail) {
    margin-bottom: 0.5rem; /* 减小新闻项之间的下边距 */
}

/* 测试区域样式 */
.test-section {
    background-color: #f5f5f5;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
}
.test-section h2 {
    color: #e74c3c;
    margin-top: 0;
}
.test-section ul {
    list-style-type: none;
    padding-left: 0;
}
.test-section li {
    padding: 8px 0;
    border-bottom: 1px dashed #eee;
}
.test-section a {
    color: #3498db;
    text-decoration: none;
}
.test-section a:hover {
    text-decoration: underline;
}

/* 独立页面导航菜单样式 */
nav .has-submenu { position: relative; }
nav .submenu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: rgba(44, 62, 80, 0.95);
    min-width: 160px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
    padding: 0.5rem 0;
    z-index: 100;
    border-radius: 0 0 4px 4px;
}
nav .has-submenu:hover .submenu,
nav .has-submenu .submenu.active { display: block; }
nav .submenu li { display: block; }
nav .submenu a {
    display: block;
    padding: 0.5rem 1rem;
    color: white;
    text-decoration: none;
}
nav .submenu a:hover {
    background-color: #3d5a80;
    color: white;
}
nav .submenu a.active {
    color: var(--accent-color);
    font-weight: bold;
}
nav .submenu a.disabled {
    color: #888;
    cursor: not-allowed;
    text-decoration: none;
}
nav .dropdown-menu a.disabled:hover, nav .submenu a.disabled:hover {
    background-color: transparent;
    color: #888;
}

nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    padding: 0.8rem 1.5rem;
    position: relative;
    display: block;
    transition: all 0.3s ease;
}

nav a:hover {
    color: var(--accent-color);
}
nav a.active {
    color: var(--accent-color);
    font-weight: bold;
}

nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--accent-color);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

nav a:hover::after,
nav a.active::after {
    transform: scaleX(1);
}

.container {
    max-width: 100%;
    width: 1200px;
    margin: 1rem auto;
    padding: 0 1rem;
    display: grid;
    gap: 1rem;
}

.contact-bar .container {
    grid-template-columns: 1fr 1fr;
    align-items: center;
}

.main-content {
    background-color: white;
    padding: 2rem;
    line-height: 1.5;
    margin-top: 0;
}

.sidebar {
    background-color: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.contact-bar {
    background-color: var(--light-green);
    padding: 2rem 0;
    margin-top: 1rem;
}

.contact-info h3,
.contact-form h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.contact-form form {
    display: grid;
    gap: 1rem;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.contact-form button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
}

.contact-form button:hover {
    background-color: #3d8b40;
}

.content-columns {
    display: flex;
    gap: 1.2rem;
    margin: 0;
    padding: 0;
}

.column {
    flex: 1;
    padding: 0 1rem;
}

/* 响应式三栏布局调整 */
@media (max-width: 768px) {
    .content-columns {
        flex-direction: column;
    }
    .column {
        margin-bottom: 1.5rem;
    }

    /* 移动端底部导航区域适配 */
    .bottom-nav .container {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem 0;
    }
    
    .bottom-nav .nav-column {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    /* 移动端友情链接区域适配 */
    .friend-links {
        padding: 1.2rem 0 1.5rem 0;
    }
    
    .friend-links .links-container {
        flex-direction: column;
        align-items: center;
        gap: 0.6rem;
        padding: 0.3rem 0;
    }
    
    .friend-links .link-card {
        width: 90%;
        margin: 0;
        text-align: center;
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    /* 移动端联系信息区域适配 */
    .contact-info-bottom {
        padding: 1.5rem 0;
    }
    
    .contact-info-bottom .container {
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 1rem 0;
        gap: 0.8rem;
    }
    
    .contact-info-bottom .contact-logo img {
        max-width: 90px;
        margin-bottom: 0.3rem;
    }
    
    .contact-details p {
        font-size: 0.95rem;
        margin: 0.2rem 0;
        line-height: 1.5;
    }
    
    /* 底部版权区域调整 */
    footer {
        padding: 0.1rem 0;
    }
    
    footer .container p {
        margin: 0.2rem 0;
        font-size: 0.85rem;
    }
}

.column:last-child {
    border-right: none;
}

.contact-info-bottom {
    background-color: #f5f5f5;
    padding: 0.5rem 0;
}
.contact-details p {
    margin: 0.2rem 0;
    line-height: 1.2;
}

.contact-info-bottom .container {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.contact-logo img {
    max-height: 80px;
}

.contact-details {
    flex: 1;
}

.news-item {
    margin-bottom: 0.8rem;
    padding-bottom: 0.8rem;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.news-image {
    flex: 0 0 150px;
}

.date-badge {
    display: inline-block;
    background-color: #4CAF50;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 4px;
    font-size: 0.8rem;
    margin-bottom: 0.2rem;
}

/* 修复悬挂的margin-bottom属性 */
.date-badge {
    margin-bottom: 0.3rem;
}

.news-image img {
    width: 100%;
    max-width: 100%;
    height: auto;
    max-height: 150px;
    object-fit: cover;
    border-radius: 4px;
}

/* 文章缩略图样式 */
.news-thumbnail {
    flex: 0 0 120px;
    height: 120px;
    overflow: hidden;
    border-radius: 4px;
}

.news-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-thumbnail img:hover {
    transform: scale(1.05);
}

/* 协会通知区域样式 */
.news-content.no-image h3 {
    font-size: 15px;
    margin: 0.05rem 0;
    line-height: 1.0;
}

section:nth-child(2) .news-item {
    margin-bottom: 0.3rem;
}

/* 减小协会通知标题间距 */
section:nth-child(2) .news-item {
    margin-top: 0;
    margin-bottom: 0.3rem;
}

.news-content {
    flex: 1;
    padding-left: 1rem;
}

.news-content.no-thumbnail {
    padding-left: 0;
    text-align: left;
}

.news-content.no-thumbnail h3 {
    margin-bottom: 0.3rem;
    line-height: 1.2;
}

.news-item h3 {
    margin-top: 0;
    margin-bottom: 0.3rem;
    font-size: 1.2rem;
    color: #2c3e50;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.news-item p {
    font-size: 0.95rem;
    line-height: 1.4;
    margin-bottom: 0.6rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.news-item a {
    color: #2980b9;
    text-decoration: none;
}

/* 最新资讯与文章标题颜色 */
.full-width-section .news-content h3 a { color: #2980b9; }

/* 隐私政策弹窗样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.4);
}

.modal-content {
    background-color: #fff;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 600px;
    border-radius: 5px;
    position: relative;
}

.modal-content h2 {
    color: var(--primary-color);
    margin-top: 0;
}

.modal-content h3 {
    color: var(--primary-light);
    margin-top: 1.5em;
    margin-bottom: 0.5em;
}

.modal-content ul {
    padding-left: 20px;
}

.modal-content li {
    margin-bottom: 0.5em;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    position: absolute;
    right: 15px;
    top: 10px;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
}

#accept-privacy {
    background-color: var(--primary-color);
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    margin-top: 20px;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

#accept-privacy:hover {
    background-color: var(--primary-light);
}

/* 轮播图样式 */
/* 轮播图样式 */
.carousel {
    position: relative;
    width: 100%;
    overflow: hidden;
    margin-top: 0;
}

.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.carousel-slide.active {
    opacity: 1;
    z-index: 1;
}

.carousel img {
    width: 100%;
    max-width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: cover;
}

.carousel-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 1rem;
    text-align: center;
}

.carousel-caption h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
}

.carousel-caption p {
    margin: 0;
    font-size: 1rem;
}

.carousel-caption .highlight {
    color: #FFC107;
    display: block;
}

.carousel-prev, .carousel-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0,0,0,0.5);
    color: white;
    border: none;
    padding: 0.8rem 1.2rem;
    cursor: pointer;
    font-size: 1.5rem;
    z-index: 100;
}

.carousel-prev {
    left: 10px;
}

.carousel-next {
    right: 10px;
}

.carousel-prev:hover, .carousel-next:hover {
    background: rgba(0, 0, 0, 0.8);
}

.carousel-indicators {
    display: none;
}

/* 面包屑导航样式 */
.breadcrumb-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 0;
    text-align: left;
}
.breadcrumb {
    margin: 0.5rem 0;
    font-size: 0.9rem;
    display: inline-block;
}
.breadcrumb a {
    color: #2980b9;
    text-decoration: none;
}
.breadcrumb a:hover {
    text-decoration: underline;
}
.category-tags {
    margin: 1rem 0;
    text-align: left;
}
.category-tags span {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    padding: 0.5rem 1.2rem;
    margin: 0 0.5rem 0.5rem 0;
    font-size: 0.9rem;
    border-radius: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    display: inline-block;
}
.category-tags span:hover {
    background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.breadcrumb-container {
    background-color: #f5f5f5;
    padding: 0.5rem 0;
    margin-bottom: 1rem;
}

.breadcrumb {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.breadcrumb a {
    color: #666;
    text-decoration: none;
    font-size: 0.9rem;
}

.breadcrumb a:hover {
    color: var(--primary-color);
}

.breadcrumb a:not(:last-child)::after {
    content: ">";
    margin: 0 0.5rem;
    color: #999;
}

.news-item a:hover {
    color: var(--accent-color);
}

.news-item p {
    font-size: 0.95rem;
    line-height: 1.7;
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .content-columns {
        flex-direction: column;
    }
    .full-width-section {
        width: 100%;
        background-color: #f3f0eb;
        padding: 3rem 0;
        margin: 0;
    }

    .section-header {
        text-align: center;
        margin-bottom: 2rem;
        padding: 0 1rem 1rem;
        border-bottom: 1px solid #e0e0e0;
    }

    .section-header h2 {
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .contact-info-bottom .container {
        flex-direction: column;
    }
    
    .news-image {
        height: 150px;
    }
    
    /* 面包屑导航移动端适配 */
    .breadcrumb-container {
        padding: 0.5rem 1rem;
        margin-bottom: 0.5rem;
    }
    
    .breadcrumb {
        font-size: 0.85rem;
        justify-content: center;
    }
    
    .breadcrumb a {
        padding: 0.2rem 0.4rem;
    }
    
    /* 导航菜单移动端适配 */
    .nav-menu {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .nav-menu li {
        width: 100%;
    }
    
    .nav-menu li a {
        padding: 1rem;
        border-bottom: 1px solid #eee;
    }
    
    .nav-menu .has-submenu .submenu {
        position: static;
        display: none;
        width: 100%;
    }
    
    .nav-menu .has-submenu:hover .submenu {
        display: block;
    }
}

/* 协会结构页面样式 */
.association-structure {
    padding: 20px 0;
    line-height: 1.8;
}

.structure-card {
    background-color: #f9f9f9;
    border-left: 4px solid #4CAF50;
    padding: 15px 20px;
    margin: 20px 0;
    border-radius: 0 5px 5px 0;
}

.structure-card h3 {
    color: #4CAF50;
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.structure-card ul {
    list-style-type: none;
    padding-left: 0;
}

.structure-card li {
    padding: 8px 0;
    border-bottom: 1px dashed #eee;
}

.structure-card li:last-child {
    border-bottom: none;
}

.section-title {
    color: #333;
    border-bottom: 2px solid #4CAF50;
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

/* Enhanced Content Sections - Sophisticated Design */
.full-width-section {
    width: 100%;
    background: linear-gradient(to bottom, #ffffff 0%, #fafafa 100%);
    padding: 4rem 0;
    margin: 0;
    border-top: 1px solid var(--border-color);
    position: relative;
}

.full-width-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(to right, transparent 0%, #e8e8e8 20%, #e8e8e8 80%, transparent 100%);
}

.full-width-section .container {
    margin: 0 auto;
    max-width: 1200px;
    padding: 0 2rem;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 0 2rem 2rem;
    border-bottom: 2px solid #e8e8e8;
    position: relative;
}

.section-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 2px;
    background: #2c3e50;
}

.section-header h2 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-family: 'Georgia', 'Times New Roman', serif;
    font-weight: 300;
    text-transform: none;
    letter-spacing: -0.5px;
    font-size: 2.2rem;
    line-height: 1.2;
}

.section-header p {
    color: #666;
    margin-top: 1rem;
    margin-bottom: 0;
    line-height: 1.6;
    font-size: 1.1rem;
    font-style: italic;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Enhanced News Layout */
.content-columns {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
    margin-top: 3rem;
}

.news-column {
    background: white;
    border: 1px solid #e8e8e8;
    transition: all 0.3s ease;
    overflow: hidden;
}

.news-column:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.column-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 1.5rem 2rem;
    text-align: center;
}

.column-header h3 {
    margin: 0;
    font-family: 'Georgia', 'Times New Roman', serif;
    font-weight: 300;
    font-size: 1.3rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.column-content {
    padding: 2rem;
}

/* Enhanced Date Badge */
.date-badge {
    background: #f8f9fa;
    color: #666;
    padding: 0.3rem 0.8rem;
    font-size: 0.75rem;
    display: inline-block;
    margin-bottom: 0.75rem;
    border: 1px solid #e8e8e8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-family: 'Arial', sans-serif;
    font-weight: 500;
}

/* Enhanced News Items */
.news-item {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #f0f0f0;
    position: relative;
}

.news-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.news-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(to bottom, #2c3e50 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.news-item:hover::before {
    opacity: 1;
}

.news-item h3 {
    margin: 0 0 0.75rem 0;
    font-size: 1.2rem;
    color: #2c3e50;
    font-family: 'Georgia', 'Times New Roman', serif;
    font-weight: 400;
    line-height: 1.4;
    padding-left: 1rem;
    transition: padding-left 0.3s ease;
}

.news-item:hover h3 {
    padding-left: 1.5rem;
}

.news-item h3 a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

.news-item h3 a:hover {
    color: #7f8c8d;
}

.news-item p {
    font-size: 0.95rem;
    line-height: 1.7;
    margin: 0.75rem 0 0 1rem;
    color: #666;
    text-align: justify;
}

/* Enhanced News Images */
.news-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    margin-bottom: 1.5rem;
    border: 1px solid #e8e8e8;
    position: relative;
}

.news-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 0%, rgba(44, 62, 80, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.news-image:hover::after {
    opacity: 1;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    filter: grayscale(10%);
}

.news-image:hover img {
    transform: scale(1.02);
    filter: grayscale(0%);
}

/* 底部导航移动端适配 */
@media (max-width: 768px) {
    .bottom-nav .container {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        padding: 1rem;
    }
    
    .contact-info-bottom {
        padding: 1rem 0;
    }
    
    .contact-logo img {
        max-width: 100px;
    }
    
    .contact-details p {
        font-size: 0.9rem;
    }
    
    /* 最新资讯与文章区域移动端适配 */
    .content-columns .column {
        width: 100%;
        margin-bottom: 1rem;
    }
}

@media (max-width: 480px) {
    .bottom-nav .container {
        grid-template-columns: 1fr;
        gap: 0.5rem;
        padding: 0.5rem;
    }
    
    .content-columns .column {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
.bottom-nav .container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
}

/* Enhanced Post Footer - Elegant Design */
.post-footer {
    border-top: 3px double #e8e8e8;
    padding: 3rem;
    margin-top: 3rem;
    background: linear-gradient(to bottom, #fafafa 0%, #f5f5f5 100%);
}

.tags-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #e8e8e8;
}

.tags-label {
    display: block;
    font-size: 0.8rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
    margin-bottom: 1rem;
    font-family: 'Arial', sans-serif;
}

.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.tags-list a {
    display: inline-block;
    background: white;
    color: #666;
    padding: 0.5rem 1.2rem;
    text-decoration: none;
    font-size: 0.85rem;
    border: 1px solid #d0d0d0;
    transition: all 0.3s ease;
    font-family: 'Georgia', 'Times New Roman', serif;
    font-style: italic;
}

.tags-list a:hover {
    background: #2c3e50;
    color: white;
    border-color: #2c3e50;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(44, 62, 80, 0.2);
}

.post-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    font-family: 'Arial', sans-serif;
}

.publication-date,
.last-modified {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-label {
    font-size: 0.8rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
}

.info-value {
    font-size: 0.95rem;
    color: #2c3e50;
    font-weight: 400;
}

/* Enhanced Post Meta Design */
.post-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    font-size: 0.9rem;
    color: #666;
    font-family: 'Arial', sans-serif;
    margin-top: 2rem;
    padding: 1.5rem;
    background: white;
    border: 1px solid #e8e8e8;
}

.meta-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.meta-label {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #999;
    font-weight: 500;
}

.meta-text {
    color: #2c3e50;
    font-weight: 400;
}

/* Enhanced Content Typography */
.post-content {
    padding: 3rem;
    line-height: 1.8;
    color: #444;
    font-size: 1.15rem;
    font-family: 'Georgia', 'Times New Roman', serif;
}

.post-content p {
    margin-bottom: 2rem;
    text-align: justify;
    text-indent: 2em;
}

.post-content p:first-of-type {
    font-size: 1.25rem;
    line-height: 1.7;
    color: #2c3e50;
    margin-bottom: 2.5rem;
}

.post-content p:first-of-type::first-letter {
    font-size: 3.5rem;
    font-weight: 300;
    line-height: 1;
    float: left;
    margin-right: 0.5rem;
    margin-top: 0.25rem;
    color: #7f8c8d;
    font-family: 'Georgia', 'Times New Roman', serif;
}

.post-content h2 {
    color: #2c3e50;
    font-size: 2rem;
    font-weight: 300;
    margin: 3rem 0 2rem 0;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #e8e8e8;
    font-family: 'Georgia', 'Times New Roman', serif;
    letter-spacing: -0.5px;
}

.post-content h3 {
    color: #34495e;
    font-size: 1.5rem;
    font-weight: 400;
    margin: 2.5rem 0 1.5rem 0;
    font-family: 'Georgia', 'Times New Roman', serif;
}

.post-content blockquote {
    margin: 2.5rem 0;
    padding: 1.5rem 2rem;
    border-left: 4px solid #bdc3c7;
    background: #f8f9fa;
    font-style: italic;
    font-size: 1.1rem;
    color: #5a6c7d;
}

.post-content ul,
.post-content ol {
    margin: 2rem 0;
    padding-left: 2.5rem;
}

.post-content li {
    margin-bottom: 0.75rem;
    line-height: 1.7;
}

/* Enhanced Sidebar Contact Styling */
.contact-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.contact-label {
    font-size: 0.75rem;
    color: #999;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.contact-text {
    font-size: 0.9rem;
    color: #2c3e50;
    line-height: 1.4;
}

/* Responsive Design Enhancement */
@media (max-width: 768px) {
    .post-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .post-header,
    .post-content,
    .post-footer {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
    
    .post-title {
        font-size: 2rem;
    }
    
    .post-meta {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .post-info {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .post-content p:first-of-type::first-letter {
        font-size: 2.5rem;
    }
}

    gap: 2rem;
    margin: 0 auto;
    max-width: 1200px;
}

/* 协会结构页面样式 */
.association-structure {
    padding: 20px 0;
    line-height: 1.8;
}

.structure-card {
    background-color: #f9f9f9;
    border-left: 4px solid #4CAF50;
    padding: 15px 20px;
    margin: 20px 0;
    border-radius: 0 5px 5px 0;
}

.structure-card h3 {
    color: #4CAF50;
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.structure-card ul {
    list-style-type: none;
    padding-left: 0;
}

.structure-card li {
    padding: 8px 0;
    border-bottom: 1px dashed #eee;
}

.structure-card li:last-child {
    border-bottom: none;
}

.section-title {
    color: #333;
    border-bottom: 2px solid #4CAF50;
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.full-width-section {
    width: 100%;
    background-color: #f3f0eb;
    padding: 3rem 0;
    margin: 0;
}

.full-width-section .container {
    margin: 0 auto;
    max-width: 1200px;
    padding: 0;
}

.section-header {
    text-align: center;
    margin-bottom: 1rem;
    padding: 0 1rem 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.section-header h2 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.date-badge {
    background-color: var(--primary-color);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    display: inline-block;
    margin-bottom: 1rem;
}

.news-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.friend-links {
    background-color: white;
    padding: 2rem 0;
    margin-top: 0;
    margin-bottom: 0;
}

.friend-links h3 {
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 1.5rem;
}

.links-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
}

.link-card {
    background-color: var(--light-green);
    color: var(--text-color);
    padding: 0.8rem 1.5rem;
    border-radius: 20px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.link-card:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}
.nav-column h4 {
    color: white;
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}
.nav-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}
.nav-column li {
    margin-bottom: 0.8rem;
}
.nav-column a {
    color: #BDC3C7;
    text-decoration: none;
}
.nav-column a:hover {
    color: var(--accent-color);
}
footer {
    background-color: var(--primary-color);
    color: white;
    text-align: center;
    padding: 0.5rem 0;
    margin-top: 0;
    line-height: 1.2;
}

footer p {
    margin: 0.3rem 0;
    font-size: 0.9rem;
    line-height: 1.2;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* 单页内容样式 */
.article-content {
    line-height: 1.8;
}

.article-header {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.article-title {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.article-content p {
    margin-bottom: 1.5rem;
}

.article-content h3 {
    color: #4CAF50;
    margin-top: 25px;
    margin-bottom: 15px;
    font-size: 1.4rem;
}

.article-content ul {
    margin-bottom: 20px;
    padding-left: 20px;
}

.article-content li {
    margin-bottom: 8px;
}

/* 响应式布局调整 */
@media (max-width: 768px) {
    .content-columns {
        flex-direction: column;
    }
    .full-width-section {
        width: 100%;
        background-color: #f3f0eb;
        padding: 3rem 0;
        margin: 0;
    }

    .section-header {
        text-align: center;
        margin-bottom: 2rem;
        padding: 0 1rem 1rem;
        border-bottom: 1px solid #e0e0e0;
    }

    .section-header h2 {
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .contact-info-bottom .container {
        flex-direction: column;
    }
    
    .news-image {
        height: 150px;
    }
}

/* 协会结构页面样式 */
.association-structure {
    padding: 20px 0;
    line-height: 1.8;
}

.structure-card {
    background-color: #f9f9f9;
    border-left: 4px solid #4CAF50;
    padding: 15px 20px;
    margin: 20px 0;
    border-radius: 0 5px 5px 0;
}

.structure-card h3 {
    color: #4CAF50;
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.structure-card ul {
    list-style-type: none;
    padding-left: 0;
}

.structure-card li {
    padding: 8px 0;
    border-bottom: 1px dashed #eee;
}

.structure-card li:last-child {
    border-bottom: none;
}

.section-title {
    color: #333;
    border-bottom: 2px solid #4CAF50;
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.full-width-section {
    width: 100%;
    background-color: #f3f0eb;
    padding: 1rem 0;
    margin: 0;
}

.full-width-section .container {
    margin: 0 auto;
    max-width: 1200px;
}

.section-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 0 1rem 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.section-header h2 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.date-badge {
    background-color: var(--primary-color);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    display: inline-block;
    margin-bottom: 1rem;
}

.news-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.friend-links {
    background-color: white;
    padding: 2rem 0;
    margin-top: 0;
    margin-bottom: 0;
}

.friend-links h3 {
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 1.5rem;
}

.links-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
}

.link-card {
    background-color: var(--light-green);
    color: var(--text-color);
    padding: 0.8rem 1.5rem;
    border-radius: 20px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.link-card:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}
.nav-column h4 {
    color: white;
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}
.nav-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}
.nav-column li {
    margin-bottom: 0.8rem;
}
.nav-column a {
    color: #BDC3C7;
    text-decoration: none;
}
.nav-column a:hover {
    color: var(--accent-color);
}
footer {
    background-color: var(--primary-color);
    color: white;
    text-align: center;
    padding: 0.3rem 0;
    margin-top: 0;
}

/* COOKIES设置按钮样式 */
.cookies-settings-btn {
    background: transparent;
    border: 1px solid #fff;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    margin-left: 15px;
    transition: all 0.3s;
}

.cookies-settings-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* COOKIES弹框样式 */
.cookies-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: none;
    justify-content: center;
    align-items: center;
}

.cookies-popup {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.cookies-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cookies-header h3 {
    margin: 0;
    color: var(--primary-color);
}

.cookies-close {
    font-size: 28px;
    cursor: pointer;
    color: #999;
}

.cookies-close:hover {
    color: #333;
}

.cookies-content {
    padding: 20px;
}

.cookies-content p {
    margin: 0 0 20px 0;
    line-height: 1.6;
}

.cookies-option {
    margin-bottom: 15px;
}

.cookies-option label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.cookies-option input[type="checkbox"] {
    margin-right: 10px;
    width: 18px;
    height: 18px;
}

.cookies-option span {
    font-size: 14px;
}

.cookies-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    text-align: right;
}

.save-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 25px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s;
}

.save-btn:hover {
    background: #1B5E20;
}
nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    padding: 0.8rem 1.5rem;
    position: relative;
    display: block;
    transition: all 0.3s ease;
}

nav a:hover {
    color: var(--accent-color);
}
nav a.active {
    color: var(--accent-color);
    font-weight: bold;
}

nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--accent-color);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

nav a:hover::after,
nav a.active::after {
    transform: scaleX(1);
}

.container {
    max-width: 100%;
    width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
    display: grid;
    gap: 1rem;
}

.contact-bar .container {
    grid-template-columns: 1fr 1fr;
    align-items: center;
}

.main-content {
    background-color: white;
    padding: 2rem;
    line-height: 1.5;
    margin-top: 0;
}

.sidebar {
    background-color: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.contact-bar {
    background-color: var(--light-green);
    padding: 2rem 0;
    margin-top: 1rem;
}

.contact-info h3,
.contact-form h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.contact-form form {
    display: grid;
    gap: 1rem;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.contact-form button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
}

.contact-form button:hover {
    background-color: #3d8b40;
}

.content-columns {
    display: flex;
    gap: 1.2rem;
    margin-bottom: 0;
    padding: 1rem 0;
}

.column {
    flex: 1;
    padding: 0 1rem;
}

/* 响应式三栏布局调整 */
@media (max-width: 768px) {
    .content-columns {
        flex-direction: column;
    }
    .column {
        margin-bottom: 1.5rem;
    }

    /* 移动端底部导航区域适配 */
.bottom-nav .bottom-nav {
    flex-direction: column;
}

.bottom-nav .nav-column {
    width: 100%;
    margin-bottom: 1rem;
}

/* 移动端友情链接区域适配 */
.friend-links .links-container {
    flex-direction: column;
    align-items: center;
}

.friend-links .link-card {
    width: 90%;
    margin: 0.5rem 0;
    text-align: center;
}

/* 确保友情链接在桌面端也居中 */
.friend-links .links-container {
    justify-content: center;
}

/* 移动端联系信息区域适配 */
.contact-info-bottom {
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.contact-info-bottom .contact-logo img {
    max-width: 120px;
    margin-bottom: 1rem;
}

/* 移动端面包屑导航适配 */
@media (max-width: 768px) {
    .breadcrumb {
        justify-content: center;
    }
    
    .breadcrumb a {
        font-size: 0.8rem;
    }
}
}

.column:last-child {
    border-right: none;
}

.contact-info-bottom {
    background-color: #f5f5f5;
    padding: 0.5rem 0;
}
.contact-details p {
    margin: 0.2rem 0;
    line-height: 1.2;
}

.contact-info-bottom .container {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.contact-logo img {
    max-height: 80px;
}

.contact-details {
    flex: 1;
}

.news-item {
    margin-bottom: 0.8rem;
    padding-bottom: 0.8rem;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.news-image {
    flex: 0 0 150px;
}

.date-badge {
    display: inline-block;
    background-color: #4CAF50;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 4px;
    font-size: 0.8rem;
    margin-bottom: 0.2rem;
}

/* 修复悬挂的margin-bottom属性 */
.date-badge {
    margin-bottom: 0.3rem;
}

.news-image img {
    width: 100%;
    max-width: 100%;
    height: auto;
    max-height: 150px;
    object-fit: cover;
    border-radius: 4px;
}

/* 文章缩略图样式 */
.news-thumbnail {
    flex: 0 0 120px;
    height: 120px;
    overflow: hidden;
    border-radius: 4px;
}

.news-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-thumbnail img:hover {
    transform: scale(1.05);
}

/* 协会通知区域样式 */
.news-content.no-image h3 {
    font-size: 15px;
    margin: 0.05rem 0;
    line-height: 1.0;
}

section:nth-child(2) .news-item {
    margin-bottom: 0.3rem;
}

/* 减小协会通知标题间距 */
section:nth-child(2) .news-item {
    margin-top: 0;
    margin-bottom: 0.3rem;
}

.news-content {
    flex: 1;
    padding-left: 1rem;
}

.news-content.no-thumbnail {
    padding-left: 0;
    text-align: left;
}

.news-content.no-thumbnail h3 {
    margin-bottom: 0.3rem;
    line-height: 1.2;
}

.news-item h3 {
    margin-top: 0;
    margin-bottom: 0.3rem;
    font-size: 1.2rem;
    color: #2c3e50;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.news-item p {
    font-size: 0.95rem;
    line-height: 1.4;
    margin-bottom: 0.6rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.news-item a {
    color: #2980b9;
    text-decoration: none;
}

/* 最新资讯与文章标题颜色 */
.full-width-section .news-content h3 a { color: #2980b9; }

/* 轮播图样式 */
/* 轮播图样式 */
.carousel {
    position: relative;
    width: 100%;
    overflow: hidden;
    margin-top: 0;
}

.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.carousel-slide.active {
    opacity: 1;
    z-index: 1;
}

.carousel img {
    width: 100%;
    max-width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: cover;
}

.carousel-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 1rem;
    text-align: center;
}

.carousel-caption h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
}

.carousel-caption p {
    margin: 0;
    font-size: 1rem;
}

.carousel-caption .highlight {
    color: #FFC107;
    display: block;
}

.carousel-prev, .carousel-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0,0,0,0.5);
    color: white;
    border: none;
    padding: 0.8rem 1.2rem;
    cursor: pointer;
    font-size: 1.5rem;
}

.carousel-prev {
    left: 10px;
}

.carousel-next {
    right: 10px;
}

.carousel-prev:hover, .carousel-next:hover {
    background: rgba(0, 0, 0, 0.8);
}

.carousel-indicators {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    display: flex;
    gap: 10px;
}

.carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    border: none;
    cursor: pointer;
    transition: background 0.3s;
}

.carousel-indicators button.active {
    background: white;
}

/* 面包屑导航样式 */
.breadcrumb-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 0;
    text-align: left;
}
.breadcrumb {
    margin: 0.5rem 0;
    font-size: 0.9rem;
    display: inline-block;
}
.breadcrumb a {
    color: #2980b9;
    text-decoration: none;
}
.breadcrumb a:hover {
    text-decoration: underline;
}
.category-tags {
    margin: 0.5rem 0 1rem;
    text-align: left;
}
.category-tags span {
    background-color: #3498db;
    color: white;
    padding: 0.5rem 1.2rem;
    margin: 0 0.5rem 0.5rem 0;
    font-size: 0.9rem;
    border-radius: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}
.category-tags span:hover {
    background-color: #1a6692;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.breadcrumb a:not(:last-child)::after {
    content: ">";
    margin: 0 0.5rem;
    color: #999;
}

.category-tags span { background: #000; color: #fff; padding: 0.3rem 0.8rem; margin-right: 0.5rem; font-size: 0.9rem; }

.news-item a:hover {
    color: var(--accent-color);
}

.news-item p {
    font-size: 0.95rem;
    line-height: 1.7;
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .content-columns {
        flex-direction: column;
    }
    .full-width-section {
        width: 100%;
        background-color: #f3f0eb;
        padding: 3rem 0;
        margin: 0;
    }

    .section-header {
        text-align: center;
        margin-bottom: 2rem;
        padding: 0 1rem 1rem;
        border-bottom: 1px solid #e0e0e0;
    }

    .section-header h2 {
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .contact-info-bottom .container {
        flex-direction: column;
    }
    
    /* 面包屑导航移动端适配 */
    .breadcrumb-container {
        padding: 0.5rem 0;
        margin-bottom: 0.5rem;
    }
    
    .breadcrumb {
        justify-content: center;
        font-size: 0.85rem;
    }
    
    .breadcrumb a {
        padding: 0 0.3rem;
    }
}

.date {
    color: #777;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    display: block;
}



.sidebar-title {
    color: var(--primary-color);
    border-left: 4px solid var(--primary-color);
    padding-left: 0.8rem;
    margin-top: 2rem;
    margin-bottom: 1.5rem;
}

.sidebar-news {
    padding-left: 1.5rem;
}

.sidebar-news li {
    margin-bottom: 0.8rem;
}

.sidebar-news a {
    color: var(--text-color);
    text-decoration: none;
}

.sidebar-news a:hover {
    color: var(--accent-color);
}

/* 底部导航样式 */
.bottom-nav {
    background-color: #2C3E50;
    padding: 2rem 0;
    color: white;
    border-top: 1px solid rgba(255,255,255,0.1);
}
.bottom-nav .container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin: 0 auto;
    max-width: 1200px;
}

/* 协会结构页面样式 */
.association-structure {
    padding: 20px 0;
    line-height: 1.8;
}

.structure-card {
    background-color: #f9f9f9;
    border-left: 4px solid #4CAF50;
    padding: 15px 20px;
    margin: 20px 0;
    border-radius: 0 5px 5px 0;
}

.structure-card h3 {
    color: #4CAF50;
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.structure-card ul {
    list-style-type: none;
    padding-left: 0;
}

.structure-card li {
    padding: 8px 0;
    border-bottom: 1px dashed #eee;
}

.structure-card li:last-child {
    border-bottom: none;
}

.section-title {
    color: #333;
    border-bottom: 2px solid #4CAF50;
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.full-width-section {
    width: 100%;
    background-color: #f3f0eb;
    padding: 3rem 0;
    margin: 2rem 0;
}

.full-width-section .container {
    margin: 0 auto;
    max-width: 1200px;
}

.section-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 0 1rem 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.section-header h2 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.date-badge {
    background-color: var(--primary-color);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    display: inline-block;
    margin-bottom: 1rem;
}

.news-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.friend-links {
    background-color: white;
    padding: 2rem 0;
    margin-top: 0;
    margin-bottom: 0;
}

.friend-links h3 {
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 1.5rem;
}

.links-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
}

.link-card {
    background-color: var(--light-green);
    color: var(--text-color);
    padding: 0.8rem 1.5rem;
    border-radius: 20px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.link-card:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}
.nav-column h4 {
    color: white;
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}
.nav-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}
.nav-column li {
    margin-bottom: 0.8rem;
}
.nav-column a {
    color: #BDC3C7;
    text-decoration: none;
}
.nav-column a:hover {
    color: var(--accent-color);
}
footer {
    background-color: var(--primary-color);
    color: white;
    text-align: center;
    padding: 0.3rem 0;
    margin-top: 0;
}

/* 单页内容样式 */
.article-content {
    line-height: 1.8;
}

.article-header {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.article-title {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.article-content p {
    margin-bottom: 1.5rem;
}

.article-content h3 {
    color: #4CAF50;
    margin-top: 25px;
    margin-bottom: 15px;
    font-size: 1.4rem;
}

.article-content ul {
    margin-bottom: 20px;
    padding-left: 20px;
}

.article-content li {
    margin-bottom: 8px;
}

/* 响应式布局调整 */
@media (max-width: 768px) {
    .content-columns {
        flex-direction: column;
    }
    .full-width-section {
        width: 100%;
        background-color: #f3f0eb;
        padding: 3rem 0;
        margin: 0;
    }

    .section-header {
        text-align: center;
        margin-bottom: 2rem;
        padding: 0 1rem 1rem;
        border-bottom: 1px solid #e0e0e0;
    }

    .section-header h2 {
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .contact-info-bottom .container {
        flex-direction: column;
    }
    
    .news-image {
        height: 150px;
    }
}

/* 协会结构页面样式 */
.association-structure {
    padding: 20px 0;
    line-height: 1.8;
}

.structure-card {
    background-color: #f9f9f9;
    border-left: 4px solid #4CAF50;
    padding: 15px 20px;
    margin: 20px 0;
    border-radius: 0 5px 5px 0;
}

.structure-card h3 {
    color: #4CAF50;
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.structure-card ul {
    list-style-type: none;
    padding-left: 0;
}

.structure-card li {
    padding: 8px 0;
    border-bottom: 1px dashed #eee;
}

.structure-card li:last-child {
    border-bottom: none;
}

.section-title {
    color: #333;
    border-bottom: 2px solid #4CAF50;
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.full-width-section {
    width: 100%;
    background-color: #f3f0eb;
    padding: 3rem 0;
    margin: 2rem 0;
}

.full-width-section .container {
    margin: 0 auto;
    max-width: 1200px;
}

.section-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 0 1rem 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.section-header h2 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.date-badge {
    background-color: var(--primary-color);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    display: inline-block;
    margin-bottom: 1rem;
}

.news-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.friend-links {
    background-color: white;
    padding: 2rem 0;
    margin-top: 0;
    margin-bottom: 0;
}

.friend-links h3 {
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 1.5rem;
}

.links-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
}

.link-card {
    background-color: var(--light-green);
    color: var(--text-color);
    padding: 0.8rem 1.5rem;
    border-radius: 20px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.link-card:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}
.nav-column h4 {
    color: white;
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}
.nav-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}
.nav-column li {
    margin-bottom: 0.8rem;
}
.nav-column a {
    color: #BDC3C7;
    text-decoration: none;
}
.nav-column a:hover {
    color: var(--accent-color);
}
footer {
    background-color: var(--primary-color);
    color: white;
    text-align: center;
    padding: 0.3rem 0;
    margin-top: 0;
}
nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    padding: 0.8rem 1.5rem;
    position: relative;
    display: block;
    transition: all 0.3s ease;
}

nav a:hover {
    color: var(--accent-color);
}
nav a.active {
    color: var(--accent-color);
    font-weight: bold;
}

nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--accent-color);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

nav a:hover::after,
nav a.active::after {
    transform: scaleX(1);
}

.container {
    max-width: 100%;
    width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
    display: grid;
    gap: 1rem;
}

.contact-bar .container {
    grid-template-columns: 1fr 1fr;
    align-items: center;
}

.main-content {
    background-color: white;
    padding: 2rem;
    line-height: 1.5;
    margin-top: 0;
}

.sidebar {
    background-color: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.contact-bar {
    background-color: var(--light-green);
    padding: 2rem 0;
    margin-top: 1rem;
}

.contact-info h3,
.contact-form h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.contact-form form {
    display: grid;
    gap: 1rem;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.contact-form button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
}

.contact-form button:hover {
    background-color: #3d8b40;
}

.content-columns {
    display: flex;
    gap: 1.2rem;
    margin-bottom: 0;
    padding: 1rem 0;
}

.column {
    flex: 1;
    padding: 0 1rem;
}

/* 响应式三栏布局调整 */
@media (max-width: 768px) {
    /* 基础字体大小调整 */
    body {
        font-size: 14px;
        line-height: 1.5;
    }
    
    h1, h2, h3, h4, h5, h6 {
        font-size: 1.1em;
    }
    
    .header-content h1 {
        font-size: 1.5rem;
    }
    
    .section-header h2 {
        font-size: 1.4rem;
    }
    
    .news-item h3 {
        font-size: 1.1rem;
    }
    
    .news-item p {
        font-size: 0.9rem;
    }
    
    .content-columns {
        flex-direction: column;
    }
    .column {
        margin-bottom: 1.5rem;
    }
    
    /* 容器宽度调整 */
    .container {
        width: 100%;
        padding: 0 0.5rem;
        margin: 1rem auto;
    }
    
    /* 图片大小调整 */
    .news-image, .news-thumbnail {
        flex: 0 0 100px;
        height: 100px;
    }
    
    .carousel img {
        max-height: 200px;
    }
    
    .carousel-caption h3 {
        font-size: 1.2rem;
    }
    
    .carousel-caption p {
        font-size: 0.9rem;
    }
    
    /* 最新资讯与文章区域响应式调整 */
    .full-width-section .content-columns {
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .full-width-section .column {
        padding: 0;
    }
    
    .full-width-section .news {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    .full-width-section .news-image {
        flex: 0 0 120px;
        width: 120px;
        height: 120px;
        margin-bottom: 1rem;
    }
    
    .full-width-section .news-content {
        padding: 0;
    }
    
    .full-width-section .date-badge {
        align-self: center;
        margin-bottom: 0.5rem;
    }

    /* 移动端底部导航区域适配 */
.bottom-nav .bottom-nav {
    flex-direction: column;
}

.bottom-nav .nav-column {
    width: 100%;
    margin-bottom: 1rem;
}

.bottom-nav .nav-column h4 {
    font-size: 1.1rem;
}

.bottom-nav .nav-column a {
    font-size: 0.9rem;
}

/* 移动端友情链接区域适配 */
.friend-links {
    padding: 1rem 0;
}

.friend-links h3 {
    font-size: 1.3rem;
}

.friend-links .links-container {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.friend-links .link-card {
    width: 90%;
    margin: 0.3rem 0;
    text-align: center;
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

/* 移动端联系信息区域适配 */
.contact-info-bottom {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1rem 0;
}

.contact-info-bottom .contact-logo img {
    max-width: 100px;
    margin-bottom: 1rem;
}

.contact-details p {
    font-size: 1rem;
    margin: 0.2rem 0;
}
}

/* 更小屏幕的适配 */
@media (max-width: 480px) {
    body {
        font-size: 13px;
    }
    
    .header-content h1 {
        font-size: 1.3rem;
    }
    
    .section-header h2 {
        font-size: 1.2rem;
    }
    
    .news-item h3 {
        font-size: 1rem;
    }
    
    .news-item p {
        font-size: 0.85rem;
    }
    
    .carousel-caption h3 {
        font-size: 1rem;
    }
    
    .carousel-caption p {
        font-size: 0.8rem;
    }
    
    .container {
        padding: 0 0.3rem;
    }
    
    /* 更小屏幕下的最新资讯与文章区域调整 */
    .full-width-section .news-image {
        flex: 0 0 100px;
        width: 100px;
        height: 100px;
    }
}

.column:last-child {
    border-right: none;
}

.contact-info-bottom {
    background-color: #f5f5f5;
    padding: 0.5rem 0;
}
.contact-details p {
    margin: 0.2rem 0;
    line-height: 1.2;
}

.contact-info-bottom .container {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.contact-logo img {
    max-height: 80px;
}

.contact-details {
    flex: 1;
}

.news-item {
    margin-bottom: 0.8rem;
    padding-bottom: 0.8rem;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.news-image {
    flex: 0 0 150px;
}

.date-badge {
    display: inline-block;
    background-color: #4CAF50;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 4px;
    font-size: 0.8rem;
    margin-bottom: 0.2rem;
}

/* 修复悬挂的margin-bottom属性 */
.date-badge {
    margin-bottom: 0.3rem;
}

.news-image img {
    width: 100%;
    max-width: 100%;
    height: auto;
    max-height: 150px;
    object-fit: cover;
    border-radius: 4px;
}

/* 文章缩略图样式 */
.news-thumbnail {
    flex: 0 0 120px;
    height: 120px;
    overflow: hidden;
    border-radius: 4px;
}

.news-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-thumbnail img:hover {
    transform: scale(1.05);
}

/* 协会通知区域样式 */
.news-content.no-image h3 {
    font-size: 15px;
    margin: 0.05rem 0;
    line-height: 1.0;
}

section:nth-child(2) .news-item {
    margin-bottom: 0.3rem;
}

/* 减小协会通知标题间距 */
section:nth-child(2) .news-item {
    margin-top: 0;
    margin-bottom: 0.3rem;
}

.news-content {
    flex: 1;
    padding-left: 1rem;
}

.news-content.no-thumbnail {
    padding-left: 0;
    text-align: left;
}

.news-content.no-thumbnail h3 {
    margin-bottom: 0.3rem;
    line-height: 1.2;
}

.news-item h3 {
    margin-top: 0;
    margin-bottom: 0.3rem;
    font-size: 1.2rem;
    color: #2c3e50;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.news-item p {
    font-size: 0.95rem;
    line-height: 1.4;
    margin-bottom: 0.6rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.news-item a {
    color: #2980b9;
    text-decoration: none;
}

/* 最新资讯与文章标题颜色 */
.full-width-section .news-content h3 a { color: #2980b9; }