$(document).ready(function() {
    // 轮播图功能
    const carouselSlides = $('.carousel-slide');
    const prevBtn = $('.carousel-prev');
    const nextBtn = $('.carousel-next');
    const indicators = $('.carousel-indicators button');
    let currentSlide = 0;
    const slideCount = carouselSlides.length;
    let slideInterval;

    // 显示当前幻灯片
    function showSlide(index) {
        // 隐藏所有幻灯片和指示器
        carouselSlides.removeClass('active');
        indicators.removeClass('active');
        // 确保索引在有效范围内
        if (index < 0) {
            currentSlide = slideCount - 1;
        } else if (index >= slideCount) {
            currentSlide = 0;
        } else {
            currentSlide = index;
        }
        // 显示当前幻灯片和指示器
        $(carouselSlides[currentSlide]).addClass('active');
        $(indicators[currentSlide]).addClass('active');
    }

    // 为指示器添加点击事件
    indicators.click(function() {
        const index = $(this).data('slide-to');
        showSlide(index);
    });

    // 下一张幻灯片
    function nextSlide() {
        showSlide(currentSlide + 1);
    }

    // 上一张幻灯片
    function prevSlide() {
        showSlide(currentSlide - 1);
    }

    // 点击事件
    nextBtn.click(nextSlide);
    prevBtn.click(prevSlide);

    // 自动轮播
    function startSlideshow() {
        slideInterval = setInterval(nextSlide, 5000); // 每5秒切换一次
    }

    // 停止轮播
    function stopSlideshow() {
        clearInterval(slideInterval);
    }

    // 鼠标悬停时停止轮播，离开时继续
    $('.carousel').mouseenter(stopSlideshow).mouseleave(startSlideshow);

    // 开始轮播
    startSlideshow();

    // 菜单切换功能
    const menuToggle = $('.menu-toggle');
    const navMenu = $('.nav-menu');

    // 添加对移动端触摸事件的支持
    menuToggle.on('click touchstart', function(e) {
        e.preventDefault();
        navMenu.toggleClass('active');
        menuToggle.toggleClass('active');
    });

    // 为不支持jQuery的环境提供原生JavaScript实现
    if (typeof $ === 'undefined' || !menuToggle.length || !navMenu.length) {
        document.addEventListener('DOMContentLoaded', function() {
            const menuToggleEl = document.querySelector('.menu-toggle');
            const navMenuEl = document.querySelector('.nav-menu');
            
            if (menuToggleEl && navMenuEl) {
                menuToggleEl.addEventListener('click', function(e) {
                    e.preventDefault();
                    navMenuEl.classList.toggle('active');
                    menuToggleEl.classList.toggle('active');
                });
                
                // 添加触摸事件支持
                menuToggleEl.addEventListener('touchstart', function(e) {
                    e.preventDefault();
                    navMenuEl.classList.toggle('active');
                    menuToggleEl.classList.toggle('active');
                });
            }
        });
    }
});