<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>协会结构 | 上海市设施农业装备行业协会</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <h1>上海市设施农业装备行业协会</h1>
    </header>
    <nav>
        <div class="menu-toggle">
            <span></span>
            <span></span>
            <span></span>
        </div>
        <ul class="nav-menu">
            <li><a href="index.html">首页</a></li>
            <li class="dropdown">
                <a href="#">协会简介</a>
                <ul class="dropdown-menu">
                    <li><a href="#">了解协会</a></li>
                    <li><a href="association-structure.html" class="active">组织结构</a></li>
                    <li><a href="#">协会负责人</a></li>
                    <li><a href="#">入会流程</a></li>
                </ul>
            </li>
            <li class="dropdown">
                <a href="#">行业动态</a>
                <ul class="dropdown-menu">
                    <li><a href="#">协会章程</a></li>
                    <li><a href="#">理事会成员</a></li>
                    <li><a href="#">协会荣誉</a></li>
                    <li><a href="#">发展历程</a></li>
                </ul>
            </li>
            <li class="dropdown">
                <a href="#">协会动态</a>
                <ul class="dropdown-menu">
                    <li><a href="#">协会风采</a></li>
                    <li><a href="#">协会活动</a></li>
                </ul>
            </li>
            <li><a href="#">会员单位</a></li>
            <li><a href="#">公告</a></li>
        </ul>
    </nav>

    <div class="breadcrumb-container">
        <div class="breadcrumb">
            <a href="index.html">首页</a>
            <a href="#">协会简介</a>
            <span>协会结构</span>
        </div>

        <div class="category-tags">
            <span>协会简介</span>
            <span>组织结构</span>
        </div>
    </div>

    <div class="container">
        <div class="main-content">
            <article class="article">
                <div class="article-header">
                    <h2 class="article-title">协会结构</h2>
                </div>
                <div class="article-content">
                    <p>根据协会章程的规定，协会的组织机构由会长，副会长、理事会及秘书处组成。会长、副会长、理事单位等职位的确定由会员代表大会选举产生。秘书长由会长提名由理事会进行聘免。</p>
                    
                    <div class="structure-card">
                        <h3>组织结构</h3>
                        <ul>
                            <li>会长：1人</li>
                            <li>副会长：10人</li>
                            <li>理事单位：35家</li>
                            <li>监事单位：1家</li>
                            <li>秘书长：1人</li>
                        </ul>
                    </div>
                    
                    <p>根据协会章程规定，会员大会是协会的最高权利机构，秘书处是理事会的日常办事机构，秘书长主持秘书处日常工作。</p>
                </div>
            </article>
        </div>
        <div class="sidebar">
            <h3 class="sidebar-title">关于协会</h3>
            <p>上海市设施农业装备行业协会是一家为广大从事设施农业装备生产、加工、出口以及相关业务的企事业单位服务的行业组织。</p>
            <h3 class="sidebar-title">联系方式</h3>
            <p>地址：上海市奉贤区金齐路1000号</p>
            <p>邮箱：<EMAIL></p>
        </div>
    </div>

    <div class="bottom-nav">
        <div class="container">
            <div class="nav-column">
                <h4>协会概况</h4>
                <ul>
                    <li><a href="#">协会简介</a></li>
                    <li><a href="association-structure.html">组织机构</a></li>
                    <li><a href="#">协会领导</a></li>
                    <li><a href="#">联系方式</a></li>
                </ul>
            </div>
            <div class="nav-column">
                <h4>行业资讯</h4>
                <ul>
                    <li><a href="#">行业动态</a></li>
                    <li><a href="#">政策法规</a></li>
                    <li><a href="#">市场行情</a></li>
                    <li><a href="#">技术交流</a></li>
                </ul>
            </div>
            <div class="nav-column">
                <h4>会员服务</h4>
                <ul>
                    <li><a href="#">会员介绍</a></li>
                    <li><a href="#">入会指南</a></li>
                    <li><a href="#">会员活动</a></li>
                </ul>
            </div>
            <div class="nav-column">
                <h4>下载中心</h4>
                <ul>
                    <li><a href="#">表格下载</a></li>
                    <li><a href="#">资料下载</a></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="friend-links">
        <div class="container">
            <h3>友情链接</h3>
            <div class="links-container">
                <a href="#" class="link-card">中国蔬菜协会</a>
                <a href="#" class="link-card">上海市农业农村委员会</a>
                <a href="#" class="link-card">宁夏蔬菜产业协会</a>
                <a href="#" class="link-card">全国农产品流通协会</a>
                <a href="#" class="link-card">上海农业科学院</a>
                <a href="#" class="link-card">中国食用菌协会</a>
            </div>
        </div>
    </div>
    <div class="contact-info-bottom">
        <div class="container">
            <div class="contact-logo">
                <img src="images/logo.jpg" alt="协会LOGO">
            </div>
            <div class="contact-details">
                <p>地址：上海市奉贤区金齐路1000号</p>
                <p>邮箱：<EMAIL></p>
            </div>
        </div>
    </div>
    <footer>
        <p>上海市设施农业装备行业协会 @2025 版权所有  沪ICP备000000000</p>
    </footer>

    <script>
        // 菜单切换功能
        function toggleMenu() {
            const navMenu = document.querySelector('.nav-menu');
            const menuToggle = document.querySelector('.menu-toggle');
            navMenu.classList.toggle('active');
            menuToggle.classList.toggle('active');
        }

        // 设置下拉菜单
        function setupDropdownMenus() {
            const dropdowns = document.querySelectorAll('.dropdown');
            dropdowns.forEach(dropdown => {
                const link = dropdown.querySelector('a:first-child');
                if (link) {
                    // 点击事件处理
                    link.addEventListener('click', function(e) {
                        // 阻止默认行为
                        e.preventDefault();
                        e.stopPropagation();

                        // 切换当前下拉菜单的active状态
                        dropdown.classList.toggle('active');

                        // 关闭其他下拉菜单
                        dropdowns.forEach(otherDropdown => {
                            if (otherDropdown !== dropdown) {
                                otherDropdown.classList.remove('active');
                            }
                        });
                    });

                    // 触摸事件处理
                    link.addEventListener('touchstart', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        dropdown.classList.toggle('active');

                        dropdowns.forEach(otherDropdown => {
                            if (otherDropdown !== dropdown) {
                                otherDropdown.classList.remove('active');
                            }
                        });
                    }, { passive: false });
                }
            });

            // 点击页面其他地方关闭下拉菜单
            document.addEventListener('click', function() {
                dropdowns.forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            });
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 菜单切换功能
            const menuToggle = document.querySelector('.menu-toggle');
            if (menuToggle) {
                menuToggle.addEventListener('click', toggleMenu);
            }

            // 初始化二级菜单
            setupDropdownMenus();
        });
    </script>
</body>
</html>