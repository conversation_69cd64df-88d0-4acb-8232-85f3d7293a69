:root {
    --primary-color: #4CAF50; /* 主色调：浅绿色 */
    --secondary-color: #F5F5F5; /* 背景色：浅灰色 */
    --accent-color: #FF9800; /* 强调色：橙色 */
    --text-color: #333333; /* 文本颜色：深灰色 */
    --light-green: #E8F5E9; /* 浅绿色背景 */
    --border-color: #e0e0e0; /* 边框颜色 */
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    margin: 0;
    padding: 0;
    line-height: 1.4;
    color: var(--text-color);
    background-color: var(--secondary-color);
}

header {
    background-color: var(--primary-color);
    color: white;
    padding: 1rem 0;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

nav {
    background-color: #2C3E50;
    padding: 0.5rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}
.menu-toggle {
    display: none;
    cursor: pointer;
    position: absolute;
    right: 1.8rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 100;
}
.menu-toggle span {
    display: block;
    width: 25px;
    height: 3px;
    background-color: white;
    margin: 5px 0;
    border-radius: 3px;
}

nav ul {
    list-style: none;
    padding: 0;
}
.nav-menu {
    display: flex;
    justify-content: center;
    gap: 2.2rem;
    margin: 0 auto;
    padding: 0;
    list-style: none;
    flex-wrap: nowrap;
}
@media (max-width: 768px) {    .menu-toggle {        display: block;    }    .nav-menu {        display: none;        flex-direction: column;        position: absolute;        top: 100%;        left: 0;        right: 0;        background-color: #2C3E50;        padding: 1rem;        box-shadow: 0 8px 16px rgba(0,0,0,0.2);    }    .nav-menu.active {        display: flex;    }    /* 移动设备上二级菜单样式 */    nav .dropdown-menu {        position: static;        margin-left: 1rem;        border-left: 2px solid rgba(255,255,255,0.2);        box-shadow: none;    }    /* 菜单按钮激活状态 */    .menu-toggle.active span:nth-child(1) {        transform: rotate(45deg) translate(5px, 5px);    }    .menu-toggle.active span:nth-child(2) {        opacity: 0;    }    .menu-toggle.active span:nth-child(3) {        transform: rotate(-45deg) translate(7px, -7px);    }
    .nav-menu li {
        margin: 0;
    }
    nav .dropdown-menu {
        position: static;
        margin-left: 1rem;
        border-left: 2px solid rgba(255,255,255,0.2);
        box-shadow: none;
    }
}
nav .dropdown {
    position: relative;
}
nav .dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: #2C3E50;
    min-width: 160px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    padding: 0.5rem 0;
    z-index: 100;
}

/* 确保触摸设备上的二级菜单也能正常显示 */
nav .dropdown.hover .dropdown-menu, nav .dropdown:hover .dropdown-menu {
    display: block;
}
nav .dropdown:hover .dropdown-menu {
    display: block;
}
nav .dropdown-menu li {
    display: block;
}
nav .dropdown-menu a {
    display: block;
    padding: 0.5rem 1rem;
    color: white;
    text-decoration: none;
}
nav .dropdown-menu a:hover {
    background-color: #3d5a80;
    color: white;
}

nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    padding: 0.6rem 1.2rem;
    position: relative;
    display: block;
}
nav a.active {
    background-color: var(--primary-color);
    color: white;
    font-weight: bold;
    border-radius: 4px;
}

nav a:hover::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--accent-color);
}

.container {
    max-width: 100%;
    width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
    display: grid;
    gap: 1rem;
}

.contact-bar .container {
    grid-template-columns: 1fr 1fr;
    align-items: center;
}

.main-content {
    background-color: white;
    padding: 2rem;
    line-height: 1.5;
    margin-top: 0;
}

.sidebar {
    background-color: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.contact-bar {
    background-color: var(--light-green);
    padding: 2rem 0;
    margin-top: 1rem;
}

.contact-info h3,
.contact-form h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.contact-form form {
    display: grid;
    gap: 1rem;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.contact-form button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
}

.contact-form button:hover {
    background-color: #3d8b40;
}

.content-columns {
    display: flex;
    gap: 1.2rem;
    margin-bottom: 0;
    padding: 1rem 0;
}

.column {
    flex: 1;
    padding: 0 1rem;
}

.column:last-child {
    border-right: none;
}

.contact-info-bottom {
    background-color: #f5f5f5;
    padding: 0.5rem 0;
}
.contact-details p {
    margin: 0.2rem 0;
    line-height: 1.2;
}

.contact-info-bottom .container {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.contact-logo img {
    max-height: 80px;
}

.contact-details {
    flex: 1;
}

.news-item {
    margin-bottom: 0.8rem;
    padding-bottom: 0.8rem;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.news-image {
    flex: 0 0 150px;
}

.date-badge {
    display: inline-block;
    background-color: #4CAF50;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 4px;
    font-size: 0.8rem;
}
    margin-bottom: 0.2rem;
}

/* 修复悬挂的margin-bottom属性 */
.date-badge {
    margin-bottom: 0.3rem;
}

.news-image img {
    width: 100%;
    max-width: 100%;
    height: auto;
    max-height: 150px;
    object-fit: cover;
    border-radius: 4px;
}

/* 协会通知区域样式 */
.news-content.no-image h3 {
    font-size: 15px;
    margin: 0.05rem 0;
    line-height: 1.0;
}

section:nth-child(2) .news-item {
    margin-bottom: 0.3rem;
}

/* 减小协会通知标题间距 */
section:nth-child(2) .news-item {
    margin-top: 0;
    margin-bottom: 0.3rem;
}

.news-content {
    flex: 1;
}

.news-item h3 {
    margin-top: 0;
    margin-bottom: 0.3rem;
    font-size: 1.2rem;
    color: #2c3e50;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.news-item p {
    font-size: 0.95rem;
    line-height: 1.4;
    margin-bottom: 0.6rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.news-item a {
    color: #2980b9;
    text-decoration: none;
}

/* 最新资讯与文章标题颜色 */
.full-width-section .news-content h3 a { color: #2980b9; }

/* 面包屑导航样式 */
.breadcrumb-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 0;
    text-align: left;
}
.breadcrumb {
    margin: 0.5rem 0;
    font-size: 0.9rem;
    display: inline-block;
}
.breadcrumb a {
    color: #2980b9;
    text-decoration: none;
}
.breadcrumb a:hover {
    text-decoration: underline;
}
.category-tags {
    margin: 0.5rem 0 1rem;
    text-align: left;
}
.category-tags span {
    background-color: #3498db;
    color: white;
    padding: 0.5rem 1.2rem;
    margin: 0 0.5rem 0.5rem 0;
    font-size: 0.9rem;
    border-radius: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}
.category-tags span:hover {
    background-color: #1a6692;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.breadcrumb a:not(:last-child)::after {
    content: ">";
    margin: 0 0.5rem;
    color: #999;
}

.category-tags span { background: #000; color: #fff; padding: 0.3rem 0.8rem; margin-right: 0.5rem; font-size: 0.9rem; }

.news-item a:hover {
    color: var(--accent-color);
}

.news-item p {
    font-size: 0.95rem;
    line-height: 1.7;
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .content-columns {
        flex-direction: column;
    }
    .full-width-section {
        width: 100%;
        background-color: #f3f0eb;
        padding: 3rem 0;
        margin: 0;
    }

    .section-header {
        text-align: center;
        margin-bottom: 2rem;
        padding: 0 1rem 1rem;
        border-bottom: 1px solid #e0e0e0;
    }

    .section-header h2 {
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .contact-info-bottom .container {
        flex-direction: column;
    }
}

.date {
    color: #777;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    display: block;
}

.carousel {
    position: relative;
    width: 100%;
    overflow: hidden;
    margin-top: 0;
}

.carousel-slide {
    display: none;
    width: 100%;
}

.carousel-slide.active {
    display: block;
}

.carousel img {
    width: 100%;
    max-width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: cover;
}

.carousel-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 1rem;
    text-align: center;
}

.carousel-caption h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
}

.carousel-caption p {
    margin: 0;
    font-size: 1rem;
}

.carousel-prev, .carousel-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0,0,0,0.5);
    color: white;
    border: none;
    padding: 0.8rem 1.2rem;
    cursor: pointer;
    font-size: 1.5rem;
}

.carousel-prev {
    left: 0;
}

.carousel-next {
    right: 0;
}

.sidebar-title {
    color: var(--primary-color);
    border-left: 4px solid var(--primary-color);
    padding-left: 0.8rem;
    margin-top: 2rem;
    margin-bottom: 1.5rem;
}

.sidebar-news {
    padding-left: 1.5rem;
}

.sidebar-news li {
    margin-bottom: 0.8rem;
}

.sidebar-news a {
    color: var(--text-color);
    text-decoration: none;
}

.sidebar-news a:hover {
    color: var(--accent-color);
}

.bottom-nav {
    background-color: #2C3E50;
    padding: 2rem 0;
    color: white;
    border-top: 1px solid rgba(255,255,255,0.1);
}
.bottom-nav .container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin: 0 auto;
    max-width: 1200px;
}

.friend-links {
    background-color: white;
    padding: 2rem 0;
    margin-top: 0;
}

.friend-links h3 {
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 1.5rem;
}

.links-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
}

.link-card {
    background-color: var(--light-green);
    color: var(--text-color);
    padding: 0.8rem 1.5rem;
    border-radius: 20px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.link-card:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}
.nav-column h4 {
    color: white;
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}
.nav-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}
.nav-column li {
    margin-bottom: 0.8rem;
}
.nav-column a {
    color: #BDC3C7;
    text-decoration: none;
}
.nav-column a:hover {
    color: var(--accent-color);
}
footer {
    background-color: var(--primary-color);
    color: white;
    text-align: center;
    padding: 0.5rem 0;
    margin-top: 0;
}
footer p {
    margin: 0;
    line-height: 1.2;
}

.article-header {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.article-title {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.article-meta {
    color: #777;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.article-content {
    line-height: 1.8;
}

.article-content p {
    margin-bottom: 1.5rem;
}

.related-news {
    margin-top: 2rem;
}

.related-item {
    margin-bottom: 1rem;
    padding-left: 0.5rem;
    border-left: 2px solid var(--primary-color);
}